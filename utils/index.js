import _ from 'lodash'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'tough-cookie'
import moment from 'moment'
import $ from 'jquery'
import OpenAI from 'openai'; // Import OpenAI

// 重新导出常量
export * from './constant.js'
export { formatTextWithAI, streamTextWithAI } from './ai.js'; // Export AI utilities

// Set to store API URLs that failed due to CORS in the current session
export const corsFailedApiUrls = new Set();

// Helper function to check for AbortError
export const isAbortError = (error) => {
  return error && error.name === 'AbortError';
};

// Helper function to parse think content from streaming response
export const parseThinkContent = (content) => {
  const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
  const matches = [];
  let match;
  let lastIndex = 0;
  let result = '';

  while ((match = thinkRegex.exec(content)) !== null) {
    // Add content before think tag
    result += content.slice(lastIndex, match.index);

    // Store think content
    matches.push({
      content: match[1],
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });

    lastIndex = match.index + match[0].length;
  }

  // Add remaining content after last think tag
  result += content.slice(lastIndex);

  return {
    cleanContent: result,
    thinkBlocks: matches
  };
};

// Helper function to extract reasoning content from stream chunk
export const extractReasoningFromChunk = (chunk) => {
  try {
    if (chunk && chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
      const delta = chunk.choices[0].delta;
      return {
        reasoning: delta.reasoning || '',
        content: delta.content || ''
      };
    }
  } catch (error) {
    console.warn('Error extracting reasoning from chunk:', error);
  }
  return {
    reasoning: '',
    content: ''
  };
};

// Function to get a configured OpenAI client, deciding whether to use a proxy
export const getOpenAIClientForCalling = (selectedApi, isViaProxy = false) => {
  if (!selectedApi || !selectedApi.apiUrl) {
    throw new Error('Invalid selectedApi or apiUrl missing in getOpenAIClientForCalling');
  }

  // Ensure isViaProxy is a boolean
  const actualIsViaProxy = typeof isViaProxy === 'boolean' ? isViaProxy : false;

  const effectiveApiKey = selectedApi.key === 'system-default' ? undefined : selectedApi.apiKey;
  // tempBaseURL is the original API's base URL (e.g., https://api.gptacg.top/v1)
  const tempBaseURL = selectedApi.apiUrl.replace(/\/chat\/completions\/?$/, '').replace(/\/$/, '');

  let finalEffectiveBaseURL;
  let attemptViaProxy = actualIsViaProxy; // Use the coerced boolean value

  if (selectedApi.key === 'system-default') {
    finalEffectiveBaseURL = `${window.location.origin}${tempBaseURL}`;
    // For system default, attemptViaProxy remains actualIsViaProxy, as it's not subject to CORS retry in the same way.
  } else { // For custom APIs
    if (actualIsViaProxy || corsFailedApiUrls.has(selectedApi.apiUrl)) {
      // Removed console.warn from here
      finalEffectiveBaseURL = `http://localhost:3001/api/openai/${tempBaseURL}`;
      attemptViaProxy = true;
    } else {
      finalEffectiveBaseURL = tempBaseURL;
      attemptViaProxy = false;
    }
  }

  // Determine if the call is to our own backend proxy or system default (which also goes through our backend)
  const isProxyOrSystemDefaultCall = finalEffectiveBaseURL.startsWith('http://localhost:3001/api/openai/') ||
                                   (selectedApi.key === 'system-default' && finalEffectiveBaseURL.startsWith(window.location.origin));

  const customFetch = (url, options) => {
    const fetchOptions = {
      ...options,
      // Only include credentials if it's a call to our own backend proxy or system default
      credentials: isProxyOrSystemDefaultCall ? 'include' : 'omit',
    };
    return fetch(url, fetchOptions);
  };

  try {
    const openai = new OpenAI({
      baseURL: finalEffectiveBaseURL,
      apiKey: effectiveApiKey || 'NA_FRONTEND_SYSTEM_DEFAULT', // Provide a default if undefined
      dangerouslyAllowBrowser: true,
      fetch: customFetch, // Use the new customFetch
      maxRetries: 0, // Disable OpenAI client's internal retries
    });
    return { openai, attemptViaProxy, usedBaseURL: finalEffectiveBaseURL };
  } catch (instantiationError) {
    console.error(`[getOpenAIClientForCalling] Error instantiating OpenAI client with baseURL ${finalEffectiveBaseURL}:`, instantiationError);
    throw instantiationError; // Re-throw the error to be handled by the caller
  }
};

export class Req {

    constructor({ jar = undefined, store = {}, engine = 'GM_fetch' } = {}) {

        if (typeof document == 'undefined') return;

        // 如果 jar 为 false，则不处理 cookie
        const shouldHandleCookies = jar !== false;

        if (shouldHandleCookies) {
            store.key = store.key || 'jar';
            store.get = store.get || GM_getValue;
            store.set = store.set || GM_setValue;
            jar = jar || CookieJar.fromJSON(JSON.stringify(store.get(store.key)) || (new CookieJar).serializeSync())
        }

        // 创建统一的请求引擎
        const createRequestEngine = async (engineType) => {
            if (engineType === 'GM_xmlhttpRequest') {
                return this.createXMLHttpEngine();
            } else {
                const GM_fetch = (await import('@sec-ant/gm-fetch')).default;
                return this.createFetchEngine(GM_fetch);
            }
        };

        return async (url, opt = {}) => {

            const requestEngine = await createRequestEngine(engine);

            let redirectEnd = false, maxRedirects = 8, redirectCount = 0;
            let method = opt.method || 'GET', body = opt.body || undefined;
            let { referer, origin } = opt.headers || {}

            while (1) {

                if (redirectEnd) break;
                if (redirectCount == maxRedirects) break;



                // 使用统一的请求引擎
                const resp = await requestEngine(url, {
                    ...opt,
                    method,
                    body,
                    headers: _.pickBy({
                        ...(shouldHandleCookies ? { cookie: jar.getCookieStringSync(url) } : {}),
                        ...opt.headers || {},
                        referer, origin,
                    }, v => !!v),
                });

                const location = resp['headers'].get('location')

                let nextURL
                if (location) {
                    redirectEnd = false;
                    redirectCount++;
                    method = 'GET';
                    body = undefined;

                    nextURL = (new URL(location, url))
                    if (nextURL.protocol == 'https:') {
                        referer = undefined;
                        origin = undefined;
                    } else {
                        referer = url;
                        origin = (new URL(url)).origin
                    }

                } else {
                    redirectEnd = true;
                }

                // 共享的 cookie 处理逻辑
                if (shouldHandleCookies) {
                    await this.handleCookies(resp, jar, url, store);
                }

                // 共享的内容处理逻辑
                await this.processResponseContent(resp);

                if (redirectEnd) return resp;
                url = nextURL.href;
            }

        }
    }

    // 创建 fetch 引擎
    createFetchEngine(GM_fetch) {
        return async (url, opt) => {
            return await GM_fetch(url, {
                ...opt,
                credentials: opt.credentials || 'omit',
                redirect: opt.redirect || 'manual',
            });
        };
    }

    // 创建 XMLHttpRequest 引擎
    createXMLHttpEngine() {
        return (url, opt) => {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: opt.method || 'GET',
                    url: url,
                    headers: opt.headers || {},
                    data: opt.body,
                    responseType: 'arraybuffer',
                    timeout: opt.timeout || 15000,
                    onload: (response) => {
                        // 将 GM_xmlhttpRequest 响应转换为类似 fetch 的格式
                        const headers = new Map();

                        // 解析响应头
                        if (response.responseHeaders) {
                            response.responseHeaders.split('\r\n').forEach(line => {
                                const [key, ...valueParts] = line.split(':');
                                if (key && valueParts.length > 0) {
                                    const value = valueParts.join(':').trim();
                                    headers.set(key.toLowerCase(), value);
                                }
                            });
                        }

                        // 创建兼容的响应对象
                        const compatResponse = {
                            status: response.status,
                            statusText: response.statusText,
                            headers: {
                                get: (name) => headers.get(name.toLowerCase()),
                                getSetCookie: () => {
                                    const setCookies = [];
                                    headers.forEach((value, key) => {
                                        if (key === 'set-cookie') {
                                            setCookies.push(value);
                                        }
                                    });
                                    return setCookies;
                                }
                            },
                            response: response.response,
                            // 添加兼容方法
                            text: async () => new TextDecoder().decode(response.response),
                            blob: async () => {
                                // 获取正确的 MIME 类型
                                const contentType = headers.get('content-type') || 'application/octet-stream';
                                return new Blob([response.response], { type: contentType });
                            }
                        };

                        resolve(compatResponse);
                    },
                    onerror: () => reject(new Error('Network error')),
                    ontimeout: () => reject(new Error('Request timeout'))
                });
            });
        };
    }

    // 共享的 cookie 处理逻辑
    async handleCookies(resp, jar, url, store) {
        const setCookieArr = resp.headers.getSetCookie();
        setCookieArr.map(item => jar.setCookieSync(item, url));

        await Promise.all((await GM.cookie.list({ url })).map(({ name }) =>
            new Promise(resolve => GM_cookie.delete({ url, name }, resolve))
        ));

        store.set(store.key, jar.serializeSync());
        await Promise.all(jar.getCookiesSync(url).map(item => GM_cookie.set({
            ...item,
            url,
            name: item.key,
            expirationDate: moment(item.expires == Infinity ? '2050-05-05' : item.expires).second(),
        })));
    }

    // 共享的内容处理逻辑
    async processResponseContent(resp) {
        const contentType = resp['headers'].get('content-type');

        if (contentType && contentType.startsWith('text/html')) {
            resp['data'] = await resp.text();
        }

        if (contentType && contentType.startsWith('image/')) {
            const blob = await resp.blob();
            resp['data'] = await new Promise(resolve => {
                const r = new FileReader();
                r.readAsDataURL(blob);
                r.onload = function () {
                    resolve(this.result);
                };
            });
        }
    }
}

export async function getDirModules(dir) {

    let importFns = {}
    if (dir == '../entries') {
        importFns = import.meta.glob('../entries/*')
    }
    if (dir == '../parsers') {
        importFns = import.meta.glob('../parsers/*')
    }

    return Promise.all(_.map(importFns).map(fn => fn()))
}

export const getEntries = () => getDirModules('../entries');
export const getParsers = () => getDirModules('../parsers');

export const match2regex = ma => ma.replace(/\.|\*|\/|\?/g, m => ({ ".": "\\.", "*": ".*", "/": "\\/", "?": "\\?" }[m]));

export const content2Html = (c = []) => c.map(({ tag, children, id, ...props }) => { // Destructure id separately

    let p = ''
    // Iterate over props, explicitly excluding 'id'
    Object.keys(props).filter(k => k !== 'id').map(k => {
        if (props[k] != undefined) p += ` ${k}="${props[k]}"`;
    })

    // Ensure children is a string, default to empty string if null/undefined
    const childContent = children === null || children === undefined ? '' : String(children);

    return `<${tag}${p}>${childContent}</${tag}>`
}).join('\r\n');

export const html2content = (htmlString = '') => {
    // Ensure jQuery is available
    if (typeof $ === 'undefined') {
        console.error("jQuery ($) is not available. Cannot parse HTML.");
        return [];
    }
    // Create a temporary div to hold the parsed HTML and parse the string
    const tempDiv = $('<div></div>').html(htmlString || ''); // Ensure htmlString is not null/undefined
    const content = [];

    // Iterate over the direct children elements of the temporary div
    tempDiv.children().each((index, element) => {
        const $el = $(element);
        const tag = $el.prop('tagName').toLowerCase();
        const item = { tag };

        // Extract attributes
        $.each(element.attributes, (i, attr) => {
            // Only add attributes that have a value
            if (attr.specified && attr.value !== null && attr.value !== undefined) {
                // Use camelCase for common attributes like 'class' -> 'className' if needed for React,
                // but for generic data structure, keep original names.
                // Let's stick to original names based on the example output.
                item[attr.name] = attr.value;
            }
        });

        // Extract children (inner HTML)
        // Use innerHTML to capture nested tags correctly. Trim whitespace.
        const childrenHtml = $el.html();
        if (childrenHtml && childrenHtml.trim()) {
            item.children = childrenHtml.trim();
        } else if ($.inArray(tag, ['img', 'br', 'hr', 'input']) === -1 && !childrenHtml) {
            // If it's not a known self-closing tag and has no innerHTML, represent as empty children
            // This might need refinement based on how empty tags should be represented.
            // Based on the example, <p>aaaa</p> -> {tag:'p', children:'aaaa'}
            // If input was <p></p>, output should likely be {tag:'p', children:''}
            item.children = '';
        }
        // For self-closing tags like <img>, 'children' might not be relevant if attributes like 'src' exist.
        // The current logic handles this implicitly as $el.html() would be empty.

        content.push(item);
    });

    // After processing children, check for any direct text nodes in the tempDiv
    // that are not just whitespace. If found, consider the format invalid.
    let hasRootTextNodes = false;
    tempDiv.contents().each((_, node) => {
        // Node.TEXT_NODE === 3
        if (node.nodeType === 3 && node.nodeValue && node.nodeValue.trim() !== '') {
            hasRootTextNodes = true;
            return false; // break .each loop
        }
    });

    if (hasRootTextNodes) {
        console.warn("html2content: Input HTML string contains unescaped root-level text nodes, which is considered invalid. Returning empty array. Input:", htmlString); // Keep this for debugging if necessary
        return []; // Signal error / invalid format
    }

    return content;
};

export const contentParser = async (parser = []) => {

    const checkImmer = () => $('body').attr('data-immersive-translate-walked') ? true : false;
    const translated = checkImmer()
    const dp = () => {
        document.dispatchEvent(new KeyboardEvent('keydown', {
            key: 'e', keyCode: 69, altKey: true, bubbles: true, cancalable: true,
        }))
    }
    if (translated) dp()
    await sleep(1500)

    const content = (await Promise.all([...$(parser.map(([selector]) => selector).join(','))].map(el => new Promise(async resolve => {
        const [selector, tag, fn] = parser.find(([selector]) => $(el).is(selector)) || []
        if (tag == 'table' && !fn) {
            const $tb = $(el).clone();
            [...$tb.find('*')].map(el => [...el.attributes].forEach(attr => {
                if (!['rowspan', 'colspan'].includes(attr.name.toLowerCase())) el.removeAttribute(attr.name)
            }))
            resolve({ tag, children: $tb.html() })
        }
        if (tag == 'img' && !fn) {
            resolve({ tag, src: $(el).attr('src') })
        }
        if (typeof fn == 'function') resolve(fn(el));
        resolve({ tag, children: $(el).text() });
    })))).flat().filter(e => !!e).map(({ children, ...other }) => {
        if (children) children = children.replace(/[\u00A0\u1680​\u180e\u2000-\u2009\u200a​\u200b​\u202f\u205f​\u3000]/g, ' ').replace(/(?:\r\n|\r|\n)/g, ' ')
        return { ...other, children }
    })

    if (translated) dp()

    return content;
}

export function sliceContentByIds(content = [], ids = []) {

    let c = [], ii = 0;
    for (let i in content) {
        i = parseInt(i)
        const item = content[i]
        if (ids.includes(item.id)) ii++;
        c[ii] = [...(c[ii] || []), item]
        if (ids.includes(item.id)) ii++;
    }
    return c;
}

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function waitFor(fn) {

    while (1) {
        if (await fn()) break;
        await sleep(500);
    }
    return true;
}

// Function to generate a simple unique ID (e.g., for React keys)
export const generateUniqueId = () => {
    return `id-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

export const checkTagCountMismatch = (translatedItems, originalItems, isTranslating) => {
    if (isTranslating) return false;
    if (!translatedItems || !originalItems || originalItems.length === 0) return false;
    const nonEmptyTranslatedItems = translatedItems.filter(item => {
        if (item === undefined || item === null) return false;
        if (typeof item === 'object' && item.type === 'text' && (!item.children || item.children.trim() === '')) return false;
        return true;
    });
    if (nonEmptyTranslatedItems.length === 0) return false;
    return nonEmptyTranslatedItems.length !== originalItems.length;
};

export const getFirstTranslatedItemText = (translatedChunks) => {
    if (!translatedChunks || translatedChunks.length === 0) {
        return '';
    }
    for (const chunk of translatedChunks) {
        if (chunk && chunk.length > 0) {
            for (const item of chunk) {
                if (item && typeof item === 'string') {
                    const textContent = item.replace(/<[^>]+>/g, '').trim();
                    if (textContent) {
                        return textContent;
                    }
                } else if (item && typeof item === 'object' && item.children && typeof item.children === 'string') {
                    const textContent = item.children.trim();
                    if (textContent) {
                        return textContent;
                    }
                }
            }
        }
    }
    return '';
};

export const getAllTextFromChunks = (currentChunks) => {
    if (!currentChunks || currentChunks.length === 0) return "";
    return currentChunks
        .map((chunkItems) => {
            return chunkItems
                .map((item) => {
                    if (!item) return "";
                    const html = content2Html([item]);
                    const div = document.createElement("div");
                    div.innerHTML = html;
                    return (div.textContent || div.innerText || "").trim();
                })
                .join(" ");
        })
        .join(" ")
        .replace(/\s+/g, " ")
        .trim();
};