// utils/ai.js
import OpenAI from 'openai'; // Needed for error type checking like OpenAI.APIConnectionError
import { getOpenAIClientForCalling, corsFailedApiUrls } from './index.js';

const DEFAULT_CHUNK_SIZE = 20; // Number of lines per chunk

/**
 * Core function to format a single block of text using an AI model.
 * Handles client instantiation, API calls, and basic error/CORS retry.
 * This is an internal helper function.
 */
async function _coreFormatSingleBlock(
  textBlock,
  systemPrompt,
  selectedApiConfig,
  modelName,
  signal,
  isViaProxyInternal = false // Internal flag for CORS retry mechanism
) {
  // Basic validation for this internal function's direct inputs
  if (!textBlock || typeof textBlock !== 'string' || textBlock.trim() === '') {
    console.log('[_coreFormatSingleBlock] No text block provided.');
    return { formattedText: null, error: 'No text block provided for formatting' };
  }
  // Other parameters (systemPrompt, selectedApiConfig, modelName) are assumed to be validated by the caller.

  console.log('[_coreFormatSingleBlock] Formatting a block of text.');

  if (signal?.aborted) {
    console.log('[_coreFormatSingleBlock] Operation cancelled before AI call for block.');
    return { formattedText: null, error: 'Operation cancelled by user' };
  }

  let openai;
  let attemptViaProxy;
  let usedBaseURL;

  try {
    const clientInfo = getOpenAIClientForCalling(selectedApiConfig, isViaProxyInternal);
    openai = clientInfo.openai;
    attemptViaProxy = clientInfo.attemptViaProxy;
    usedBaseURL = clientInfo.usedBaseURL;
  } catch (instantiationError) {
    console.error('[_coreFormatSingleBlock] Error instantiating OpenAI client:', instantiationError);
    return { formattedText: null, error: `AI客户端初始化失败: ${instantiationError.message}` };
  }

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: textBlock }
  ];

  try {
    const completion = await openai.chat.completions.create({
      model: modelName,
      messages: messages,
      stream: false,
      temperature: 0.3,
    }, { signal });

    if (signal?.aborted) {
      console.log('[_coreFormatSingleBlock] Operation cancelled after AI call for block.');
      return { formattedText: null, error: 'Operation cancelled by user' };
    }

    const aiResponse = completion.choices?.[0]?.message?.content?.trim();
    if (!aiResponse) {
      console.warn('[_coreFormatSingleBlock] AI did not return formatted content for block.');
      return { formattedText: null, error: 'AI did not return formatted content for block' };
    }

    // 清理AI响应，移除可能的额外格式标记
    let cleanedResponse = aiResponse;

    // 移除markdown代码块标记
    cleanedResponse = cleanedResponse.replace(/^```(?:text|markdown)?\s*\n?/i, '');
    cleanedResponse = cleanedResponse.replace(/\n?\s*```\s*$/i, '');

    // 移除常见的AI响应前缀
    const responsePrefixes = [
      /^以下是格式化后的.*?：\s*/i,
      /^格式化后的.*?：\s*/i,
      /^Here\s+is\s+the\s+formatted.*?:\s*/i,
      /^The\s+formatted.*?:\s*/i,
      /^根据要求.*?：\s*/i,
      /^按照要求.*?：\s*/i
    ];

    for (const prefix of responsePrefixes) {
      cleanedResponse = cleanedResponse.replace(prefix, '');
    }

    // 移除常见的AI响应后缀
    const responseSuffixes = [
      /\s*希望这.*$/i,
      /\s*以上是.*$/i,
      /\s*这样格式化.*$/i,
      /\s*Please\s+let\s+me\s+know.*$/i,
      /\s*Let\s+me\s+know.*$/i
    ];

    for (const suffix of responseSuffixes) {
      cleanedResponse = cleanedResponse.replace(suffix, '');
    }

    cleanedResponse = cleanedResponse.trim();

    return { formattedText: cleanedResponse, error: null };

  } catch (error) {
    if (signal?.aborted || error.name === 'AbortError') {
      console.log('[_coreFormatSingleBlock] AI call aborted for block:', error.message);
      return { formattedText: null, error: 'Operation cancelled by user' };
    }

    if (
      !attemptViaProxy &&
      selectedApiConfig.key !== 'system-default' &&
      error &&
      (
        (error instanceof OpenAI.APIConnectionError) ||
        (error instanceof TypeError && error.message && (error.message.toLowerCase().includes('failed to fetch') || error.message.toLowerCase().includes('networkerror')))
      )
    ) {
      console.warn(`[_coreFormatSingleBlock] Suspected CORS or network error for API ${selectedApiConfig.provider} (${usedBaseURL}) for block. Adding to failed list and retrying via proxy. Error:`, error.message);
      corsFailedApiUrls.add(selectedApiConfig.apiUrl);
      // Recursive call for THIS block with proxy enabled
      return _coreFormatSingleBlock(
        textBlock,
        systemPrompt,
        selectedApiConfig,
        modelName,
        signal,
        true // Retry this specific block via proxy
      );
    }

    console.error('[_coreFormatSingleBlock] Error calling AI for text formatting block:', error);
    return { formattedText: null, error: `AI call failed for block: ${error.message}` };
  }
}

/**
 * Helper function to process tasks in parallel with a concurrency limit.
 */
async function _processBatchTasks(tasks, asyncOperation, concurrencyLevel, overallSignal) {
  const results = new Array(tasks.length);
  const queue = [...tasks.entries()]; // [index, task]
  let activePromisesCount = 0;
  let completedCount = 0;

  if (tasks.length === 0) {
    return Promise.resolve([]);
  }

  return new Promise((resolve, reject) => {
    function runNextTask() {
      if (overallSignal?.aborted) {
        // If aborted, ensure any pending tasks don't start, and resolve/reject appropriately.
        // For simplicity, we'll let currently active ones finish and then the main formatTextWithAI will check signal.
        // Or, we could try to cancel them if asyncOperation respects its own signal.
        if (completedCount === tasks.length && activePromisesCount === 0) {
          resolve(results);
        }
        return;
      }

      if (completedCount === tasks.length) {
        if (activePromisesCount === 0) {
          resolve(results);
        }
        return;
      }

      while (activePromisesCount < concurrencyLevel && queue.length > 0) {
        if (overallSignal?.aborted) { // Double check
          if (completedCount === tasks.length && activePromisesCount === 0) resolve(results);
          return;
        }

        const [index, task] = queue.shift();
        activePromisesCount++;
        
        // Create a new AbortController for each task, linked to the overallSignal if possible,
        // or just pass overallSignal. For simplicity, pass overallSignal.
        // asyncOperation should ideally handle this signal.
        asyncOperation(task, index, overallSignal)
          .then(result => {
            results[index] = result;
          })
          .catch(error => { // Should not happen if asyncOperation returns error objects
            results[index] = { error: `Unexpected error in task ${index}: ${error.message}` };
          })
          .finally(() => {
            activePromisesCount--;
            completedCount++;
            if (completedCount === tasks.length) {
              if (activePromisesCount === 0) {
                resolve(results);
              }
            } else {
              runNextTask();
            }
          });
      }
    }

    const initialBatchSize = Math.min(concurrencyLevel, tasks.length);
    for (let i = 0; i < initialBatchSize; i++) {
      if (overallSignal?.aborted) break;
      runNextTask();
    }
    if (initialBatchSize === 0 || (overallSignal?.aborted && activePromisesCount === 0 && completedCount === tasks.length)) {
        resolve(results);
    }
  });
}


/**
 * A generalized utility function to format text using an AI model.
 * Handles client instantiation, API calls, basic error/CORS retry,
 * and optional chunking for large inputs.
 *
 * @param {string} textToFormat - The raw text to be formatted.
 * @param {string} systemPrompt - The system prompt to guide the AI.
 * @param {object} selectedApiConfig - The API configuration object.
 * @param {string} modelName - The name of the AI model to use.
 * @param {AbortSignal} [signal] - Optional AbortSignal to cancel the request.
 * @param {number} [concurrencyLevel=1] - Concurrency level for batch processing.
 * @param {boolean} [disableChunking=false] - Optional flag to disable chunking and process text as a single block.
 * @returns {Promise<{formattedText: string|null, error: string|null}>}
 */
export async function formatTextWithAI(
  textToFormat,
  systemPrompt,
  selectedApiConfig,
  modelName,
  signal,
  concurrencyLevel = 1, // Default concurrency to 1 if not chunking or not specified
  disableChunking = false
) {
  // Initial validations
  if (!textToFormat || typeof textToFormat !== 'string' || textToFormat.trim() === '') {
    console.log('[formatTextWithAI] No text provided to format.');
    return { formattedText: null, error: null };
  }
  if (!systemPrompt || typeof systemPrompt !== 'string') {
    console.warn('[formatTextWithAI] System prompt is missing or invalid.');
    return { formattedText: null, error: 'System prompt is missing or invalid.' };
  }
  if (!selectedApiConfig || typeof selectedApiConfig !== 'object') {
    console.warn('[formatTextWithAI] API configuration is missing or invalid.');
    return { formattedText: null, error: 'API configuration is missing or invalid.' };
  }
  if (!modelName || typeof modelName !== 'string') {
    console.warn('[formatTextWithAI] Model name is missing or invalid.');
    return { formattedText: null, error: 'Model name is missing or invalid.' };
  }
  if (typeof concurrencyLevel !== 'number' || concurrencyLevel < 1) {
    console.warn(`[formatTextWithAI] Invalid concurrencyLevel: ${concurrencyLevel}. Defaulting to 1.`);
    concurrencyLevel = 1;
  }

  if (signal?.aborted) {
    console.log('[formatTextWithAI] Operation cancelled before processing.');
    return { formattedText: null, error: 'Operation cancelled by user' };
  }

  const lines = textToFormat.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  if (disableChunking || lines.length <= DEFAULT_CHUNK_SIZE) {
    const reason = disableChunking ? "Chunking disabled by caller" : `Text has ${lines.length} lines (<= ${DEFAULT_CHUNK_SIZE})`;
    console.log(`[formatTextWithAI] ${reason}, processing as a single block.`);
    return _coreFormatSingleBlock(textToFormat, systemPrompt, selectedApiConfig, modelName, signal);
  }

  // Chunking logic (only if not disabled and lines > DEFAULT_CHUNK_SIZE)
  console.log(`[formatTextWithAI] Text has ${lines.length} lines (> ${DEFAULT_CHUNK_SIZE}), chunking into blocks of ${DEFAULT_CHUNK_SIZE} lines with concurrency ${concurrencyLevel}.`);
  const lineChunks = [];
  for (let i = 0; i < lines.length; i += DEFAULT_CHUNK_SIZE) {
    lineChunks.push(lines.slice(i, i + DEFAULT_CHUNK_SIZE));
  }
  const textBlockChunks = lineChunks.map(chunk => chunk.join('\n'));
  console.log(`[formatTextWithAI] Created ${textBlockChunks.length} text block chunks.`);

  const asyncOperationForBatch = (textBlockChunk, chunkIndex, taskSignal) => {
    console.log(`[formatTextWithAI] Formatting chunk ${chunkIndex + 1}/${textBlockChunks.length}`);
    return _coreFormatSingleBlock(textBlockChunk, systemPrompt, selectedApiConfig, modelName, taskSignal);
  };

  try {
    const chunkResults = await _processBatchTasks(
      textBlockChunks,
      asyncOperationForBatch,
      concurrencyLevel,
      signal // Overall signal for the batch processing
    );

    if (signal?.aborted) {
      console.log('[formatTextWithAI] Batch formatting operation cancelled.');
      return { formattedText: null, error: 'Operation cancelled by user' };
    }

    const errors = [];
    const successfulFormattedTexts = [];
    for (const result of chunkResults) {
      if (result.error) {
        errors.push(result.error);
      } else if (result.formattedText) {
        successfulFormattedTexts.push(result.formattedText);
      } else {
        // Should not happen if _coreFormatSingleBlock guarantees error or formattedText
        errors.push('A chunk processing returned no text and no error.');
      }
    }

    if (errors.length > 0) {
      const aggregatedError = `Failed to format ${errors.length} out of ${textBlockChunks.length} chunks. Errors: ${errors.join('; ')}`;
      console.error(`[formatTextWithAI] ${aggregatedError}`);
      // Optionally, return partial results if some succeeded:
      // if (successfulFormattedTexts.length > 0) {
      //   return { formattedText: successfulFormattedTexts.join('\n'), error: aggregatedError };
      // }
      return { formattedText: null, error: aggregatedError };
    }

    if (successfulFormattedTexts.length === 0 && textBlockChunks.length > 0) {
        // All chunks processed but no text returned (e.g. all returned null formattedText without error)
        return { formattedText: null, error: 'All chunks processed but yielded no formatted text.' };
    }


    const finalFormattedText = successfulFormattedTexts.join('\n'); // Join formatted chunks with a single newline
    console.log('[formatTextWithAI] Successfully formatted all text chunks.');
    return { formattedText: finalFormattedText, error: null };

  } catch (batchError) { // Should be rare if _processBatchTasks resolves with results array
    console.error('[formatTextWithAI] Unexpected error during batch processing:', batchError);
    return { formattedText: null, error: `Batch processing failed: ${batchError.message}` };
  }
}

/**
 * Performs a streaming AI chat completion.
 *
 * @param {string} userContent - The user's input text.
 * @param {string} systemPrompt - The system prompt.
 * @param {object} selectedApiConfig - API configuration object.
 * @param {string} modelName - The name of the AI model to use.
 * @param {AbortSignal | null} signal - AbortController signal for cancellation.
 * @param {function(object): (boolean | void)} onChunk - Callback function to process each stream chunk.
 *                                                       Receives an object like { content?: string, reasoning?: string, error?: string, isFinal?: boolean, rawChunk?: object }.
 *                                                       Return false to stop further stream processing.
 * @param {boolean} [isViaProxyInternal=false] - Internal flag for CORS retry via proxy.
 * @returns {Promise<{success: boolean, error?: string}>} - Resolves when stream ends or is aborted/errored.
 */
export async function streamTextWithAI(
  userContent,
  systemPrompt,
  selectedApiConfig,
  modelName,
  signal,
  onChunk,
  isViaProxyInternal = false
) {
  console.log(`[DEBUG streamTextWithAI] Called. Model: ${modelName}, API: ${selectedApiConfig?.provider}, isProxy: ${isViaProxyInternal}, UserContent: "${userContent.substring(0,50)}..."`);

  // Validations
  if (!userContent || typeof userContent !== 'string') {
    const errorMsg = 'User content is missing or invalid.';
    console.warn(`[streamTextWithAI] Validation Error: ${errorMsg}`);
    onChunk({ error: errorMsg, isFinal: true });
    return { success: false, error: errorMsg };
  }
  if (!systemPrompt || typeof systemPrompt !== 'string') {
    const errorMsg = 'System prompt is missing or invalid.';
    console.warn(`[streamTextWithAI] Validation Error: ${errorMsg}`);
    onChunk({ error: errorMsg, isFinal: true });
    return { success: false, error: errorMsg };
  }
  if (!selectedApiConfig || typeof selectedApiConfig !== 'object') {
    const errorMsg = 'API configuration is missing or invalid.';
    console.warn(`[streamTextWithAI] Validation Error: ${errorMsg}`);
    onChunk({ error: errorMsg, isFinal: true });
    return { success: false, error: errorMsg };
  }
  if (!modelName || typeof modelName !== 'string') {
    const errorMsg = 'Model name is missing or invalid.';
    console.warn(`[streamTextWithAI] Validation Error: ${errorMsg}`);
    onChunk({ error: errorMsg, isFinal: true });
    return { success: false, error: errorMsg };
  }
  if (typeof onChunk !== 'function') {
    const errorMsg = 'onChunk callback is missing or not a function.';
    console.error(`[streamTextWithAI] CRITICAL Error: ${errorMsg}`); // This is a critical programming error
    return { success: false, error: errorMsg };
  }

  if (signal?.aborted) {
    const errorMsg = 'Operation cancelled by user before starting stream.';
    console.log(`[DEBUG streamTextWithAI] ${errorMsg}`);
    onChunk({ error: errorMsg, isFinal: true });
    return { success: false, error: errorMsg };
  }

  let openai;
  let attemptViaProxy;
  let usedBaseURL;

  try {
    console.log(`[DEBUG streamTextWithAI] Attempting to get OpenAI client. API: ${selectedApiConfig.apiUrl}, Proxy: ${isViaProxyInternal}`);
    const clientInfo = getOpenAIClientForCalling(selectedApiConfig, isViaProxyInternal);
    openai = clientInfo.openai;
    attemptViaProxy = clientInfo.attemptViaProxy;
    usedBaseURL = clientInfo.usedBaseURL;
    console.log(`[DEBUG streamTextWithAI] OpenAI client obtained. Used URL: ${usedBaseURL}, AttemptProxyNext: ${attemptViaProxy}`);
  } catch (instantiationError) {
    const errorMsg = `AI客户端初始化失败: ${instantiationError.message}`;
    console.error('[streamTextWithAI] Error instantiating OpenAI client:', instantiationError);
    onChunk({ error: errorMsg, isFinal: true });
    return { success: false, error: errorMsg };
  }

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userContent },
  ];
  console.log(`[DEBUG streamTextWithAI] Messages prepared for API call:`, JSON.stringify(messages).substring(0, 200) + "...");

  try {
    console.log(`[DEBUG streamTextWithAI] Calling openai.chat.completions.create (stream)... Model: ${modelName}`);
    const completionStream = await openai.chat.completions.create({
      model: modelName,
      messages: messages,
      stream: true,
      temperature: 0.3, // Or make configurable
    }, { signal });
    console.log(`[DEBUG streamTextWithAI] openai.chat.completions.create call successful, stream object received.`);

    for await (const chunk of completionStream) {
      console.log(`[DEBUG streamTextWithAI] Received raw chunk from stream:`, JSON.stringify(chunk));
      if (signal?.aborted) {
        const errorMsg = 'Operation cancelled by user during stream.';
        console.log(`[DEBUG streamTextWithAI] ${errorMsg}`);
        onChunk({ error: errorMsg, isFinal: true }); // Notify caller
        return { success: false, error: errorMsg };
      }

      const content = chunk.choices[0]?.delta?.content;
      // Attempt to get reasoning directly from a 'reasoning' field in the delta,
      // based on user feedback that AI provides "reasoner" info.
      let reasoningOutput = chunk.choices[0]?.delta?.reasoning;

      if (reasoningOutput) { // Check if reasoningOutput is not null, undefined, or empty string
        // console.log(`[DEBUG streamTextWithAI] Extracted reasoningOutput directly from delta.reasoning: "${reasoningOutput}"`); // Log removed as per user request
      }
      // If 'reasoningOutput' is still null/undefined here, we will need further clarification
      // on how "reasoner" information is provided by the AI.
      
      // console.log(`[DEBUG streamTextWithAI] Calling onChunk with: content="${content}", reasoning="${reasoningOutput || null}"`); // Log removed as per user request
      const shouldContinue = onChunk({ content, reasoning: reasoningOutput || null, rawChunk: chunk });
      console.log(`[DEBUG streamTextWithAI] onChunk returned: ${shouldContinue}`);

      if (shouldContinue === false) {
        console.log('[streamTextWithAI] Stream processing stopped by onChunk callback.');
        onChunk({ isFinal: true, reason: 'Stopped by callback' });
        console.log(`[DEBUG streamTextWithAI] Returning { success: true } after onChunk stop.`);
        return { success: true }; // Stream was processed as far as requested
      }
    }

    // Stream finished naturally
    console.log(`[DEBUG streamTextWithAI] Stream finished naturally. Calling onChunk({ isFinal: true }).`);
    onChunk({ isFinal: true });
    console.log(`[DEBUG streamTextWithAI] Returning { success: true } after natural stream end.`);
    return { success: true };

  } catch (error) {
    if (signal?.aborted || error.name === 'AbortError') {
      const errorMsg = 'Operation cancelled by user during AI call.';
      console.log(`[DEBUG streamTextWithAI] AbortError caught. ${errorMsg}`, error.message);
      onChunk({ error: errorMsg, isFinal: true });
      return { success: false, error: errorMsg };
    }

    if (
      !attemptViaProxy &&
      selectedApiConfig.key !== 'system-default' &&
      error &&
      (
        (error instanceof OpenAI.APIConnectionError) ||
        (error instanceof TypeError && error.message && (error.message.toLowerCase().includes('failed to fetch') || error.message.toLowerCase().includes('networkerror')))
      )
    ) {
      const errorMsgForLog = `Suspected CORS or network error for API ${selectedApiConfig.provider} (${usedBaseURL}). Adding to failed list and retrying via proxy. Error: ${error.message}`;
      console.warn(`[streamTextWithAI] ${errorMsgForLog}`);
      corsFailedApiUrls.add(selectedApiConfig.apiUrl);
      // Recursive call with proxy enabled
      return streamTextWithAI(
        userContent,
        systemPrompt,
        selectedApiConfig,
        modelName,
        signal,
        onChunk,
        true // Retry via proxy
      );
    }

    const errorMsg = `AI stream call failed: ${error.message}`;
    console.error('[streamTextWithAI] Error during AI stream call:', error);
    onChunk({ error: errorMsg, isFinal: true });
    return { success: false, error: errorMsg };
  }
}